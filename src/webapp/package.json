{"name": "liveclient", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@testing-library/jest-dom": "^5.0.0", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/jest": "^24.0.0", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@types/react-router-dom": "^5.1.3", "antd": "^3.26.7", "artplayer": "^4.6.2", "copy-to-clipboard": "^3.3.3", "mpegts.js": "^1.7.3", "prismjs": "^1.29.0", "react": "^16.12.0", "react-dom": "^16.12.0", "react-router-dom": "^5.1.2", "react-scripts": "^3.x.x", "react-simple-code-editor": "^0.13.1", "typescript": "~5.0.4"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://127.0.0.1:8080/", "devDependencies": {"@types/prismjs": "^1.26.0"}}