// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/bililive-go/bililive-go/src/recorders (interfaces: <PERSON><PERSON>,Manager)
//
// Generated by this command:
//
//	mockgen -package recorders -destination mock_test.go github.com/bililive-go/bililive-go/src/recorders Recorder,Manager
//

// Package recorders is a generated GoMock package.
package recorders

import (
	context "context"
	reflect "reflect"
	time "time"

	live "github.com/bililive-go/bililive-go/src/live"
	types "github.com/bililive-go/bililive-go/src/types"
	gomock "go.uber.org/mock/gomock"
)

// MockRecorder is a mock of Recorder interface.
type MockRecorder struct {
	ctrl     *gomock.Controller
	recorder *MockRecorderMockRecorder
	isgomock struct{}
}

// MockRecorderMockRecorder is the mock recorder for MockRecorder.
type MockRecorderMockRecorder struct {
	mock *MockRecorder
}

// NewMockRecorder creates a new mock instance.
func NewMockRecorder(ctrl *gomock.Controller) *MockRecorder {
	mock := &MockRecorder{ctrl: ctrl}
	mock.recorder = &MockRecorderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecorder) EXPECT() *MockRecorderMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockRecorder) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockRecorderMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockRecorder)(nil).Close))
}

// GetStatus mocks base method.
func (m *MockRecorder) GetStatus() (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatus")
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatus indicates an expected call of GetStatus.
func (mr *MockRecorderMockRecorder) GetStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatus", reflect.TypeOf((*MockRecorder)(nil).GetStatus))
}

// Start mocks base method.
func (m *MockRecorder) Start(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockRecorderMockRecorder) Start(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockRecorder)(nil).Start), ctx)
}

// StartTime mocks base method.
func (m *MockRecorder) StartTime() time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartTime")
	ret0, _ := ret[0].(time.Time)
	return ret0
}

// StartTime indicates an expected call of StartTime.
func (mr *MockRecorderMockRecorder) StartTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTime", reflect.TypeOf((*MockRecorder)(nil).StartTime))
}

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
	isgomock struct{}
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// AddRecorder mocks base method.
func (m *MockManager) AddRecorder(ctx context.Context, arg1 live.Live) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRecorder", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRecorder indicates an expected call of AddRecorder.
func (mr *MockManagerMockRecorder) AddRecorder(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRecorder", reflect.TypeOf((*MockManager)(nil).AddRecorder), ctx, arg1)
}

// Close mocks base method.
func (m *MockManager) Close(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close", ctx)
}

// Close indicates an expected call of Close.
func (mr *MockManagerMockRecorder) Close(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockManager)(nil).Close), ctx)
}

// GetRecorder mocks base method.
func (m *MockManager) GetRecorder(ctx context.Context, liveId types.LiveID) (Recorder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecorder", ctx, liveId)
	ret0, _ := ret[0].(Recorder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecorder indicates an expected call of GetRecorder.
func (mr *MockManagerMockRecorder) GetRecorder(ctx, liveId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecorder", reflect.TypeOf((*MockManager)(nil).GetRecorder), ctx, liveId)
}

// HasRecorder mocks base method.
func (m *MockManager) HasRecorder(ctx context.Context, liveId types.LiveID) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasRecorder", ctx, liveId)
	ret0, _ := ret[0].(bool)
	return ret0
}

// HasRecorder indicates an expected call of HasRecorder.
func (mr *MockManagerMockRecorder) HasRecorder(ctx, liveId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasRecorder", reflect.TypeOf((*MockManager)(nil).HasRecorder), ctx, liveId)
}

// RemoveRecorder mocks base method.
func (m *MockManager) RemoveRecorder(ctx context.Context, liveId types.LiveID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveRecorder", ctx, liveId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveRecorder indicates an expected call of RemoveRecorder.
func (mr *MockManagerMockRecorder) RemoveRecorder(ctx, liveId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRecorder", reflect.TypeOf((*MockManager)(nil).RemoveRecorder), ctx, liveId)
}

// RestartRecorder mocks base method.
func (m *MockManager) RestartRecorder(ctx context.Context, liveId live.Live) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestartRecorder", ctx, liveId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestartRecorder indicates an expected call of RestartRecorder.
func (mr *MockManagerMockRecorder) RestartRecorder(ctx, liveId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartRecorder", reflect.TypeOf((*MockManager)(nil).RestartRecorder), ctx, liveId)
}

// Start mocks base method.
func (m *MockManager) Start(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockManagerMockRecorder) Start(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockManager)(nil).Start), ctx)
}
