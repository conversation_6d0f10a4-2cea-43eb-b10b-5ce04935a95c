// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/bililive-go/bililive-go/src/live (interfaces: Live)
//
// Generated by this command:
//
//	mockgen -package mock -destination mock/mock.go github.com/bililive-go/bililive-go/src/live Live
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	url "net/url"
	reflect "reflect"
	time "time"

	configs "github.com/bililive-go/bililive-go/src/configs"
	live "github.com/bililive-go/bililive-go/src/live"
	types "github.com/bililive-go/bililive-go/src/types"
	gomock "go.uber.org/mock/gomock"
)

// MockLive is a mock of Live interface.
type MockLive struct {
	ctrl     *gomock.Controller
	recorder *MockLiveMockRecorder
	isgomock struct{}
}

// MockLiveMockRecorder is the mock recorder for MockLive.
type MockLiveMockRecorder struct {
	mock *MockLive
}

// NewMockLive creates a new mock instance.
func NewMockLive(ctrl *gomock.Controller) *MockLive {
	mock := &MockLive{ctrl: ctrl}
	mock.recorder = &MockLiveMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLive) EXPECT() *MockLiveMockRecorder {
	return m.recorder
}

// GetInfo mocks base method.
func (m *MockLive) GetInfo() (*live.Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInfo")
	ret0, _ := ret[0].(*live.Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInfo indicates an expected call of GetInfo.
func (mr *MockLiveMockRecorder) GetInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInfo", reflect.TypeOf((*MockLive)(nil).GetInfo))
}

// GetLastStartTime mocks base method.
func (m *MockLive) GetLastStartTime() time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastStartTime")
	ret0, _ := ret[0].(time.Time)
	return ret0
}

// GetLastStartTime indicates an expected call of GetLastStartTime.
func (mr *MockLiveMockRecorder) GetLastStartTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastStartTime", reflect.TypeOf((*MockLive)(nil).GetLastStartTime))
}

// GetLiveId mocks base method.
func (m *MockLive) GetLiveId() types.LiveID {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiveId")
	ret0, _ := ret[0].(types.LiveID)
	return ret0
}

// GetLiveId indicates an expected call of GetLiveId.
func (mr *MockLiveMockRecorder) GetLiveId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveId", reflect.TypeOf((*MockLive)(nil).GetLiveId))
}

// GetOptions mocks base method.
func (m *MockLive) GetOptions() *live.Options {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptions")
	ret0, _ := ret[0].(*live.Options)
	return ret0
}

// GetOptions indicates an expected call of GetOptions.
func (mr *MockLiveMockRecorder) GetOptions() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptions", reflect.TypeOf((*MockLive)(nil).GetOptions))
}

// GetPlatformCNName mocks base method.
func (m *MockLive) GetPlatformCNName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlatformCNName")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPlatformCNName indicates an expected call of GetPlatformCNName.
func (mr *MockLiveMockRecorder) GetPlatformCNName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlatformCNName", reflect.TypeOf((*MockLive)(nil).GetPlatformCNName))
}

// GetRawUrl mocks base method.
func (m *MockLive) GetRawUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRawUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetRawUrl indicates an expected call of GetRawUrl.
func (mr *MockLiveMockRecorder) GetRawUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRawUrl", reflect.TypeOf((*MockLive)(nil).GetRawUrl))
}

// GetStreamInfos mocks base method.
func (m *MockLive) GetStreamInfos() ([]*live.StreamUrlInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStreamInfos")
	ret0, _ := ret[0].([]*live.StreamUrlInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStreamInfos indicates an expected call of GetStreamInfos.
func (mr *MockLiveMockRecorder) GetStreamInfos() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStreamInfos", reflect.TypeOf((*MockLive)(nil).GetStreamInfos))
}

// GetStreamUrls mocks base method.
func (m *MockLive) GetStreamUrls() ([]*url.URL, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStreamUrls")
	ret0, _ := ret[0].([]*url.URL)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStreamUrls indicates an expected call of GetStreamUrls.
func (mr *MockLiveMockRecorder) GetStreamUrls() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStreamUrls", reflect.TypeOf((*MockLive)(nil).GetStreamUrls))
}

// SetLastStartTime mocks base method.
func (m *MockLive) SetLastStartTime(arg0 time.Time) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLastStartTime", arg0)
}

// SetLastStartTime indicates an expected call of SetLastStartTime.
func (mr *MockLiveMockRecorder) SetLastStartTime(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLastStartTime", reflect.TypeOf((*MockLive)(nil).SetLastStartTime), arg0)
}

// SetLiveIdByString mocks base method.
func (m *MockLive) SetLiveIdByString(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLiveIdByString", arg0)
}

// SetLiveIdByString indicates an expected call of SetLiveIdByString.
func (mr *MockLiveMockRecorder) SetLiveIdByString(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLiveIdByString", reflect.TypeOf((*MockLive)(nil).SetLiveIdByString), arg0)
}

// UpdateLiveOptionsbyConfig mocks base method.
func (m *MockLive) UpdateLiveOptionsbyConfig(arg0 context.Context, arg1 *configs.LiveRoom) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveOptionsbyConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLiveOptionsbyConfig indicates an expected call of UpdateLiveOptionsbyConfig.
func (mr *MockLiveMockRecorder) UpdateLiveOptionsbyConfig(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveOptionsbyConfig", reflect.TypeOf((*MockLive)(nil).UpdateLiveOptionsbyConfig), arg0, arg1)
}
