// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/bililive-go/bililive-go/src/pkg/events (interfaces: Dispatcher)
//
// Generated by this command:
//
//	mockgen -package mock -destination mock/mock.go github.com/bililive-go/bililive-go/src/pkg/events Dispatcher
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	events "github.com/bililive-go/bililive-go/src/pkg/events"
	gomock "go.uber.org/mock/gomock"
)

// MockDispatcher is a mock of Dispatcher interface.
type MockDispatcher struct {
	ctrl     *gomock.Controller
	recorder *MockDispatcherMockRecorder
	isgomock struct{}
}

// MockDispatcherMockRecorder is the mock recorder for MockDispatcher.
type MockDispatcherMockRecorder struct {
	mock *MockDispatcher
}

// NewMockDispatcher creates a new mock instance.
func NewMockDispatcher(ctrl *gomock.Controller) *MockDispatcher {
	mock := &MockDispatcher{ctrl: ctrl}
	mock.recorder = &MockDispatcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDispatcher) EXPECT() *MockDispatcherMockRecorder {
	return m.recorder
}

// AddEventListener mocks base method.
func (m *MockDispatcher) AddEventListener(eventType events.EventType, listener *events.EventListener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddEventListener", eventType, listener)
}

// AddEventListener indicates an expected call of AddEventListener.
func (mr *MockDispatcherMockRecorder) AddEventListener(eventType, listener any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEventListener", reflect.TypeOf((*MockDispatcher)(nil).AddEventListener), eventType, listener)
}

// Close mocks base method.
func (m *MockDispatcher) Close(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close", ctx)
}

// Close indicates an expected call of Close.
func (mr *MockDispatcherMockRecorder) Close(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockDispatcher)(nil).Close), ctx)
}

// DispatchEvent mocks base method.
func (m *MockDispatcher) DispatchEvent(event *events.Event) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DispatchEvent", event)
}

// DispatchEvent indicates an expected call of DispatchEvent.
func (mr *MockDispatcherMockRecorder) DispatchEvent(event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DispatchEvent", reflect.TypeOf((*MockDispatcher)(nil).DispatchEvent), event)
}

// RemoveAllEventListener mocks base method.
func (m *MockDispatcher) RemoveAllEventListener(eventType events.EventType) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RemoveAllEventListener", eventType)
}

// RemoveAllEventListener indicates an expected call of RemoveAllEventListener.
func (mr *MockDispatcherMockRecorder) RemoveAllEventListener(eventType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAllEventListener", reflect.TypeOf((*MockDispatcher)(nil).RemoveAllEventListener), eventType)
}

// RemoveEventListener mocks base method.
func (m *MockDispatcher) RemoveEventListener(eventType events.EventType, listener *events.EventListener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RemoveEventListener", eventType, listener)
}

// RemoveEventListener indicates an expected call of RemoveEventListener.
func (mr *MockDispatcherMockRecorder) RemoveEventListener(eventType, listener any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveEventListener", reflect.TypeOf((*MockDispatcher)(nil).RemoveEventListener), eventType, listener)
}

// Start mocks base method.
func (m *MockDispatcher) Start(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockDispatcherMockRecorder) Start(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockDispatcher)(nil).Start), ctx)
}
