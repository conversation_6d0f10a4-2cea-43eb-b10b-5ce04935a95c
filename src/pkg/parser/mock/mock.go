// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/bililive-go/bililive-go/src/pkg/parser (interfaces: Parser)
//
// Generated by this command:
//
//	mockgen -package mock -destination mock/mock.go github.com/bililive-go/bililive-go/src/pkg/parser Parser
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	live "github.com/bililive-go/bililive-go/src/live"
	gomock "go.uber.org/mock/gomock"
)

// MockParser is a mock of Parser interface.
type MockParser struct {
	ctrl     *gomock.Controller
	recorder *MockParserMockRecorder
	isgomock struct{}
}

// MockParserMockRecorder is the mock recorder for MockParser.
type MockParserMockRecorder struct {
	mock *MockParser
}

// NewMockParser creates a new mock instance.
func NewMockParser(ctrl *gomock.Controller) *MockParser {
	mock := &MockParser{ctrl: ctrl}
	mock.recorder = &MockParserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParser) EXPECT() *MockParserMockRecorder {
	return m.recorder
}

// ParseLiveStream mocks base method.
func (m *MockParser) ParseLiveStream(ctx context.Context, streamUrlInfo *live.StreamUrlInfo, arg2 live.Live, file string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseLiveStream", ctx, streamUrlInfo, arg2, file)
	ret0, _ := ret[0].(error)
	return ret0
}

// ParseLiveStream indicates an expected call of ParseLiveStream.
func (mr *MockParserMockRecorder) ParseLiveStream(ctx, streamUrlInfo, arg2, file any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseLiveStream", reflect.TypeOf((*MockParser)(nil).ParseLiveStream), ctx, streamUrlInfo, arg2, file)
}

// Stop mocks base method.
func (m *MockParser) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockParserMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockParser)(nil).Stop))
}
