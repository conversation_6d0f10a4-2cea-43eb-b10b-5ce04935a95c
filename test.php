<?php

require_once 'DouyinLiveParser.php';

/**
 * 简单的测试脚本
 * 用于快速验证 DouyinLiveParser 的基本功能
 */

echo "🚀 抖音直播解析器测试\n";
echo "===================\n\n";

// 测试URL（请替换为实际的直播间URL）
$testUrl = 'https://live.douyin.com/123456789';

// 如果通过命令行传入URL参数
if (isset($argv[1])) {
    $testUrl = $argv[1];
    echo "使用命令行参数URL: {$testUrl}\n\n";
}

try {
    // 创建解析器实例
    echo "📡 创建解析器实例...\n";
    $parser = new DouyinLiveParser([], 10, true);
    
    // 解析直播间
    echo "🔍 解析直播间信息...\n";
    $roomInfo = $parser->getRoomInfo($testUrl);
    
    // 显示基本信息
    echo "\n📋 直播间信息:\n";
    echo "  主播: {$roomInfo['roomInfo']['hostName']}\n";
    echo "  标题: {$roomInfo['roomInfo']['roomName']}\n";
    echo "  状态: " . ($roomInfo['roomInfo']['status'] ? '🔴 直播中' : '⚫ 未直播') . "\n";
    
    if (!$roomInfo['roomInfo']['status']) {
        echo "\n⚠️  主播当前未在直播，无法获取流URL\n";
        exit(0);
    }
    
    // 获取流URL
    echo "\n🎥 获取流URL...\n";
    $streamResult = $parser->getStreamUrls($roomInfo, 'origin');
    
    echo "✅ 成功获取流URL:\n";
    echo "  质量: {$streamResult['quality']}\n";
    echo "  URL: {$streamResult['url']}\n";
    
    // 检查URL可用性
    echo "\n🔗 检查URL可用性...\n";
    $isAvailable = $parser->checkUrlAvailability($streamResult['url']);
    echo "结果: " . ($isAvailable ? '✅ 可用' : '❌ 不可用') . "\n";
    
    // 显示所有支持的清晰度
    echo "\n📊 支持的清晰度:\n";
    $qualities = DouyinLiveParser::getSupportedQualities();
    foreach ($qualities as $quality) {
        echo "  - {$quality}\n";
    }
    
    echo "\n🎉 测试完成！\n";
    
} catch (Exception $e) {
    echo "\n❌ 测试失败: {$e->getMessage()}\n";
    echo "\n💡 可能的原因:\n";
    echo "  1. 直播间URL无效或已失效\n";
    echo "  2. 网络连接问题\n";
    echo "  3. 抖音页面结构发生变化\n";
    echo "  4. 反爬虫机制阻止了请求\n";
    
    exit(1);
}

/**
 * 使用说明:
 * 
 * 1. 基本使用:
 *    php test.php
 * 
 * 2. 指定URL:
 *    php test.php "https://live.douyin.com/123456789"
 * 
 * 3. 环境要求:
 *    - PHP 7.4+
 *    - cURL 扩展
 *    - JSON 扩展
 */
