language: go
sudo: required
go:
  - "1.16.5"
env:
  - GO111MODULE=on NODE_VERSION=16.3.0
before_install:
  - nvm install $NODE_VERSION
script:
  - make build-web bililive
  - make test
before_deploy:
  - go install github.com/golang/mock/mockgen@v1.6.0
  - make release
deploy:
  provider: releases
  api_key:
    secure: xfefGaJVPyP2y+wvtosX7c0YmNw85ANfCIrUyfIY56++XlKLNSTUl3l38pXV0D9/MZyk4jOj+UPqfPoUskjB9uX7oVLLH3X7ESh/XlqP0J+VVxpVtL+iYyZ4BkuxzolZFV8nMJ5X6XfUnzG4Z3FFJUagpQN4acfX+WAP2zw5ILGrdcgWwun3kLvi2SuybAApHhEzsty7wOdmczVQYKLkU066jtG2Zs1gPl1IVg4k/U8mVEgiHwZdsB4QTg01lL4+qPd9xC7f+EHY33FQ+o4MkO6ZKnR3VjKfS4ufJp6KoVdat/m9RP8ykDpVL3n0fKtu+bYWjvetxGroFm75es8lD5vKpXAE/8mKLI+MltJgknzJlOep/i38i+6SDHZhLLa+6uCgNSZxd7ie5f2rsrFHp7X8pnevuUS4jGU/XwKuoKF9fGRa3AA6w3QEgWkn+sjECk9zyk9c5ADNTmoNaux6kLjg+acTYQHpnIApQEsGd/WK0bdCibs93FFqPQrXpr29gtC+w3ivSSzevVSNLdDmwm6AgtJbNAS+1M/hXAQDo7pZ5sxpoXAuZ2c3jAgz7htEolz0QCZX3SwMEaBQuem5jMW/nbrFGfrhN6y4a4vYr9z3V+VAhdF5q+s67kHs7+p1uZai/d8uPu2e17Yj7sIDbPPk4LU/gsrJKCCP9bgcT3k=
  skip_cleanup: true
  file_glob: true
  file: "bin/*"
  on:
    tags: true
after_deploy:
  - curl -fsSL https://get.docker.com | sh
  - echo '{"experimental":"enabled"}' | sudo tee /etc/docker/daemon.json
  - mkdir -p $HOME/.docker
  - echo '{"experimental":"enabled"}' | sudo tee $HOME/.docker/config.json
  - sudo service docker start
  - docker run --rm --privileged multiarch/qemu-user-static:register --reset -p yes
  - docker buildx create --name xbuilder --use
  - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
  - make release-docker
