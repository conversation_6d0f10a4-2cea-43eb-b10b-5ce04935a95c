{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.2.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "id": null, "iteration": 1646218030013, "links": [], "liveNow": false, "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "yellow", "index": 1, "text": "摸"}, "1": {"color": "green", "index": 0, "text": "播"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 0}, "hideTimeOverride": true, "id": 2, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "8.2.0", "targets": [{"exemplar": true, "expr": "bgo_live_status{instance=~\"$instance\", live_host_name=~\"$anchor\"}", "instant": true, "interval": "", "legendFormat": "{{live_host_name}}", "refId": "A"}], "timeFrom": "1m", "title": "Live Status", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 16, "x": 8, "y": 0}, "hideTimeOverride": true, "id": 8, "interval": "1m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "8.2.0", "targets": [{"exemplar": false, "expr": "sum by(live_host_name)(increase(bgo_live_duration_seconds{instance=~\"$instance\", live_host_name=~\"$anchor\"}[30d]))", "interval": "", "legendFormat": "{{live_host_name}}", "refId": "A"}], "timeFrom": "30d", "timeShift": null, "title": "Length of live stream in the past 30 days", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 12, "w": 3, "x": 0, "y": 10}, "hideTimeOverride": true, "id": 6, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "8.2.0", "targets": [{"exemplar": true, "expr": "bgo_recorder_total_bytes{instance=~\"$instance\", live_host_name=~\"$anchor\"}", "format": "time_series", "instant": true, "interval": "", "legendFormat": "{{live_host_name}}", "refId": "A"}], "timeFrom": null, "title": "Output File Size", "type": "stat"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 12, "w": 21, "x": 3, "y": 10}, "hideTimeOverride": false, "id": 4, "interval": null, "maxDataPoints": null, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.0", "targets": [{"exemplar": true, "expr": "irate(bgo_recorder_total_bytes{instance=~\"$instance\", live_host_name=~\"$anchor\"}[$__interval])", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{live_host_name}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Live Bitrate", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 31, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "bgo_live_status", "description": "bililive instance name", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "instance", "options": [], "query": {"query": "bgo_live_status", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/instance=\"(.*?)\"/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "bgo_live_status", "description": "anchor name", "error": null, "hide": 0, "includeAll": true, "label": "", "multi": true, "name": "anchor", "options": [], "query": {"query": "bgo_live_status", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/live_host_name=\"(.*?)\"/", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Bililive Dashboard", "uid": "iXqlPlBnk", "version": 20}