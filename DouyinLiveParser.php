<?php

/**
 * 抖音直播解析器 - 基于 bililive-go 项目转换
 * 
 * 功能特点：
 * - 支持双域名解析（live.douyin.com 和 v.douyin.com）
 * - 多重正则匹配策略
 * - 智能清晰度选择和降级
 * - 完整的反爬虫机制应对
 * - URL 可用性实时检测
 * 
 * <AUTHOR> from bililive-go
 * @version 1.0.0
 */
class DouyinLiveParser
{
    // 支持的域名
    const DOMAIN_WEB = 'live.douyin.com';
    const DOMAIN_APP = 'v.douyin.com';

    // 清晰度等级映射
    const QUALITY_MAP = [
        'origin' => 0,  // 原画
        'uhd'    => 1,  // 超高清
        'hd'     => 2,  // 高清
        'sd'     => 3,  // 标清
        'ld'     => 4,  // 流畅
    ];

    // 默认请求头（模拟真实浏览器）
    private array $defaultHeaders = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept-Language' => 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Referer' => 'https://live.douyin.com/',
        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Connection' => 'keep-alive',
        'Upgrade-Insecure-Requests' => '1',
        'Sec-Fetch-Dest' => 'document',
        'Sec-Fetch-Mode' => 'navigate',
        'Sec-Fetch-Site' => 'none',
        'Cookie' => 'ttwid=1%7CB1qls3GdnZhUov9o2NxOMxxYS2ff6OSvEWbv0ytbES4%7C1680522049%7C280d802d6d478e3e78d0c807f7c487e7ffec0ae4e5fdd6a0fe74c3c6af149511; my_rd=1; passport_csrf_token=3ab34460fa656183fccfb904b16ff742; passport_csrf_token_default=3ab34460fa656183fccfb904b16ff742; d_ticket=9f562383ac0547d0b561904513229d76c9c21; n_mh=hvnJEQ4Q5eiH74-84kTFUyv4VK8xtSrpRZG1AhCeFNI; store-region=cn-fj; store-region-src=uid; LOGIN_STATUS=1; __security_server_data_status=1; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; pwa2=%223%7C0%7C3%7C0%22; download_guide=%223%2F20230729%2F0%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.6%7D; strategyABtestKey=%************.923%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A150%7D%22; VIDEO_FILTER_MEMO_SELECT=%7B%22expireTime%22%3A1691443863751%2C%22type%22%3Anull%7D; home_can_add_dy_2_desktop=%221%22; __live_version__=%221.1.1.2169%22; device_web_cpu_core=8; device_web_memory_size=8; xgplayer_user_id=************; csrf_session_id=2e00356b5cd8544d17a0e66484946f28; odin_tt=724eb4dd23bc6ffaed9a1571ac4c757ef597768a70c75fef695b95845b7ffcd8b1524278c2ac31c2587996d058e03414595f0a4e856c53bd0d5e5f56dc6d82e24004dc77773e6b83ced6f80f1bb70627; __ac_nonce=064caded4009deafd8b89; __ac_signature=_02B4Z6wo00f01HLUuwwAAIDBh6tRkVLvBQBy9L-AAHiHf7; ttcid=2e9619ebbb8449eaa3d5a42d8ce88ec835; webcast_leading_last_show_time=1691016922379; webcast_leading_total_show_times=1; webcast_local_quality=sd; live_can_add_dy_2_desktop=%221%22; msToken=1JDHnVPw_9yTvzIrwb7cQj8dCMNOoesXbA_IooV8cezcOdpe4pzusZE7NB7tZn9TBXPr0ylxmv-KMs5rqbNUBHP4P7VBFUu0ZAht_BEylqrLpzgt3y5ne_38hXDOX8o=; msToken=jV_yeN1IQKUd9PlNtpL7k5vthGKcHo0dEh_QPUQhr8G3cuYv-Jbb4NnIxGDmhVOkZOCSihNpA2kvYtHiTW25XNNX_yrsv5FN8O6zm3qmCIXcEe0LywLn7oBO2gITEeg=; tt_scid=mYfqpfbDjqXrIGJuQ7q-DlQJfUSG51qG.KUdzztuGP83OjuVLXnQHjsz-BRHRJu4e986'
    ];

    private array $customHeaders = [];
    private int $timeout = 10;
    private bool $debug = false;

    /**
     * 构造函数
     * 
     * @param array $customHeaders 自定义请求头
     * @param int $timeout 请求超时时间（秒）
     * @param bool $debug 是否开启调试模式
     */
    public function __construct(array $customHeaders = [], int $timeout = 10, bool $debug = false)
    {
        $this->customHeaders = $customHeaders;
        $this->timeout = $timeout;
        $this->debug = $debug;
    }

    /**
     * 获取直播间信息
     * 
     * @param string $url 直播间URL
     * @return array 包含直播间信息和流URL的数组
     * @throws Exception 解析失败时抛出异常
     */
    public function getRoomInfo(string $url): array
    {
        $this->log("开始解析直播间: {$url}");

        // 解析URL获取域名
        $parsedUrl = parse_url($url);
        if (!$parsedUrl || !isset($parsedUrl['host'])) {
            throw new Exception('无效的URL格式');
        }

        $host = $parsedUrl['host'];

        // 根据域名选择解析策略
        if ($host === self::DOMAIN_APP) {
            $this->log('检测到App域名，尝试App版解析');
            try {
                return $this->getAppStreamData($url);
            } catch (Exception $e) {
                $this->log("App版解析失败: {$e->getMessage()}，降级到Web版");
            }
        }

        // Web版解析
        return $this->getWebStreamData($url);
    }

    /**
     * 获取指定质量的流URL
     * 
     * @param array $roomInfo 房间信息
     * @param string $quality 期望的质量等级
     * @return array 流URL信息
     * @throws Exception 获取失败时抛出异常
     */
    public function getStreamUrls(array $roomInfo, string $quality = 'origin'): array
    {
        if (!isset($roomInfo['streamUrlInfo'])) {
            throw new Exception('房间信息中缺少流URL数据');
        }

        // 构建所有质量的流信息
        $streamInfos = $this->createStreamUrlInfos(
            $roomInfo['streamUrlInfo'],
            $roomInfo['originUrlList'] ?? []
        );

        if (empty($streamInfos)) {
            throw new Exception('未找到可用的流URL');
        }

        // 获取质量索引
        [$qualityName, $qualityIndex] = $this->getQualityIndex($quality);

        // 选择指定质量的URL
        if (isset($streamInfos[$qualityIndex])) {
            $selectedUrl = $streamInfos[$qualityIndex]['url'];

            // 检查URL可用性
            if ($this->checkUrlAvailability($selectedUrl)) {
                $this->log("成功获取 {$qualityName} 质量流URL: {$selectedUrl}");
                return [
                    'url' => $selectedUrl,
                    'quality' => $qualityName,
                    'index' => $qualityIndex
                ];
            }

            // 当前质量不可用，尝试降级
            $this->log("{$qualityName} 质量不可用，尝试自动降级");
            return $this->fallbackQuality($streamInfos, $qualityIndex);
        }

        throw new Exception("未找到质量为 {$qualityName} 的流URL");
    }

    /**
     * 检查URL可用性
     * 
     * @param string $url 要检查的URL
     * @return bool 是否可用
     */
    public function checkUrlAvailability(string $url): bool
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_NOBODY => true,  // 只获取头部，不下载内容
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,    // 5秒超时
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => $this->defaultHeaders['User-Agent']
        ]);

        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $this->log("URL可用性检查失败: {$error}");
            return false;
        }

        $available = $httpCode === 200;
        $this->log("URL可用性检查: {$url} -> " . ($available ? '可用' : '不可用') . " (HTTP {$httpCode})");

        return $available;
    }

    /**
     * Web版数据获取
     * 
     * @param string $url 直播间URL
     * @return array 解析结果
     * @throws Exception 解析失败时抛出异常
     */
    private function getWebStreamData(string $url): array
    {
        // 发送HTTP请求获取页面内容
        $html = $this->httpRequest($url);

        // 解析页面内容
        return $this->parseRoomInfo($html);
    }

    /**
     * App版数据获取（简化实现）
     * 
     * @param string $url 直播间URL
     * @return array 解析结果
     * @throws Exception 解析失败时抛出异常
     */
    private function getAppStreamData(string $url): array
    {
        // App版解析逻辑（可根据需要扩展）
        throw new Exception('App版解析暂未实现，请使用Web版URL');
    }

    /**
     * 解析房间信息 - 核心解析逻辑
     * 
     * @param string $html 页面HTML内容
     * @return array 解析结果
     * @throws Exception 解析失败时抛出异常
     */
    private function parseRoomInfo(string $html): array
    {
        $this->log('开始解析房间信息');

        // 第一步：使用双重正则表达式提取JSON数据
        $jsonStr = $this->extractJsonFromHtml($html);

        // 第二步：清理和解析JSON数据
        $roomData = $this->cleanAndParseJson($jsonStr);

        // 第三步：提取房间基本信息
        $roomInfo = $this->extractRoomInfo($roomData);

        // 第四步：提取流URL信息
        $streamUrlInfo = $this->extractStreamUrlInfo($roomData);

        // 第五步：提取原画URL信息
        $originUrlList = $this->extractOriginUrlInfo($html);

        return [
            'roomInfo' => $roomInfo,
            'streamUrlInfo' => $streamUrlInfo,
            'originUrlList' => $originUrlList
        ];
    }

    /**
     * 从HTML中提取JSON数据 - 双重正则策略
     * 
     * @param string $html HTML内容
     * @return string 提取的JSON字符串
     * @throws Exception 提取失败时抛出异常
     */
    private function extractJsonFromHtml(string $html): string
    {
        // 第一个正则表达式：匹配state模式
        $pattern1 = '/(\{\\"state\\":.*?)]\\n"]\)/';
        if (preg_match($pattern1, $html, $matches1)) {
            $this->log('使用第一个正则表达式成功匹配');
            return $matches1[1];
        }

        // 第二个正则表达式：匹配common模式
        $pattern2 = '/(\{\\"common\\":.*?)]\\n"]\)<\/script><div hidden/';
        if (preg_match($pattern2, $html, $matches2)) {
            $this->log('使用第二个正则表达式成功匹配');
            return $matches2[1];
        }

        throw new Exception('无法从HTML中提取JSON数据，可能页面结构已变化');
    }

    /**
     * 清理和解析JSON数据
     * 
     * @param string $jsonStr 原始JSON字符串
     * @return array 解析后的数组
     * @throws Exception 解析失败时抛出异常
     */
    private function cleanAndParseJson(string $jsonStr): array
    {
        // 清理JSON字符串
        $cleanedString = str_replace('\\', '', $jsonStr);
        $cleanedString = str_replace('u0026', '&', $cleanedString);

        $this->log('JSON清理完成，开始解析');

        // 解析JSON
        $data = json_decode($cleanedString, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON解析失败: ' . json_last_error_msg());
        }

        return $data;
    }

    /**
     * 提取房间基本信息
     * 
     * @param array $roomData 房间数据
     * @return array 房间信息
     * @throws Exception 提取失败时抛出异常
     */
    private function extractRoomInfo(array $roomData): array
    {
        // 提取roomStore信息
        if (!isset($roomData['roomStore']['roomInfo']['room'])) {
            throw new Exception('无法找到房间信息');
        }

        $room = $roomData['roomStore']['roomInfo']['room'];

        // 提取主播信息
        $anchorName = $this->extractAnchorName($roomData);

        // 检查直播状态
        $status = $room['status'] ?? 0;
        $isStreaming = $status == 2;

        $title = $room['title'] ?? $anchorName;

        $this->log("房间信息: 主播={$anchorName}, 标题={$title}, 状态=" . ($isStreaming ? '直播中' : '未直播'));

        return [
            'hostName' => $anchorName,
            'roomName' => $title,
            'status' => $isStreaming,
            'rawStatus' => $status
        ];
    }

    /**
     * 提取主播名称
     * 
     * @param array $roomData 房间数据
     * @return string 主播名称
     */
    private function extractAnchorName(array $roomData): string
    {
        // 从roomStore中提取主播昵称
        $roomStore = json_encode($roomData['roomStore'] ?? []);

        if (preg_match('/"nickname":"(.*?)","avatar_thumb/', $roomStore, $matches)) {
            return $matches[1];
        }

        return '未知主播';
    }

    /**
     * 提取流URL信息
     * 
     * @param array $roomData 房间数据
     * @return array 流URL信息
     * @throws Exception 提取失败时抛出异常
     */
    private function extractStreamUrlInfo(array $roomData): array
    {
        $room = $roomData['roomStore']['roomInfo']['room'] ?? [];

        if (!isset($room['stream_url'])) {
            throw new Exception('无法找到流URL信息');
        }

        $streamUrlInfo = $room['stream_url'];
        $this->log('成功提取流URL信息');

        return $streamUrlInfo;
    }

    /**
     * 从HTML中提取原画URL信息
     * 
     * @param string $html HTML内容
     * @return array 原画URL信息
     */
    private function extractOriginUrlInfo(string $html): array
    {
        // 尝试提取origin信息
        $pattern = '/"origin":\{"main":(.*?),"dash"/';
        if (preg_match($pattern, $html, $matches)) {
            $originJsonStr = $matches[1] . '}';
            $originJsonStr = str_replace('\\', '', $originJsonStr);
            $originJsonStr = str_replace('u0026', '&', $originJsonStr);

            $originData = json_decode($originJsonStr, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->log('成功提取原画URL信息');
                return $originData;
            }
        }

        $this->log('未找到原画URL信息');
        return [];
    }

    /**
     * 构建不同清晰度的流信息
     *
     * @param array $streamUrlInfo 流URL信息
     * @param array $originUrlList 原画URL信息
     * @return array 构建的流信息数组
     */
    private function createStreamUrlInfos(array $streamUrlInfo, array $originUrlList): array
    {
        $streamInfos = [];

        // 处理FLV URL
        if (isset($streamUrlInfo['flv_pull_url']) && is_array($streamUrlInfo['flv_pull_url'])) {
            $flvUrls = [];
            $flvQualities = [];

            // 如果有原画URL，添加到开头（最高优先级）
            if (!empty($originUrlList) && isset($originUrlList['flv'])) {
                $originFlv = $originUrlList['flv'];

                // 添加编码参数
                if (isset($originUrlList['sdk_params']['VCodec'])) {
                    $originFlv .= '&codec=' . $originUrlList['sdk_params']['VCodec'];
                }

                $flvUrls[] = $originFlv;
                $flvQualities[] = 'ORIGIN';
                $this->log('添加原画质量URL');
            }

            // 添加其他FLV流
            foreach ($streamUrlInfo['flv_pull_url'] as $quality => $url) {
                if (is_string($url)) {
                    $flvUrls[] = $url;
                    $flvQualities[] = $quality;
                }
            }

            // 补齐逻辑：确保至少有5个质量选项
            while (count($flvUrls) < 5 && !empty($flvUrls)) {
                $flvUrls[] = end($flvUrls);
                $flvQualities[] = end($flvQualities);
            }

            // 构建流信息数组
            foreach ($flvUrls as $index => $url) {
                $quality = $flvQualities[$index];
                $streamInfos[] = [
                    'name' => $quality,
                    'description' => "FLV Stream - {$quality}",
                    'url' => $url,
                    'format' => 'flv'
                ];
            }
        }

        // 处理HLS URL（可选）
        if (isset($streamUrlInfo['hls_pull_url_map']) && is_array($streamUrlInfo['hls_pull_url_map'])) {
            foreach ($streamUrlInfo['hls_pull_url_map'] as $quality => $url) {
                if (is_string($url)) {
                    $streamInfos[] = [
                        'name' => $quality,
                        'description' => "HLS Stream - {$quality}",
                        'url' => $url,
                        'format' => 'hls'
                    ];
                }
            }
        }

        $this->log('构建了 ' . count($streamInfos) . ' 个流信息');
        return $streamInfos;
    }

    /**
     * 获取质量索引
     *
     * @param string $quality 质量名称
     * @return array [质量名称, 索引]
     */
    private function getQualityIndex(string $quality): array
    {
        if (isset(self::QUALITY_MAP[$quality])) {
            return [$quality, self::QUALITY_MAP[$quality]];
        }

        // 默认返回高清质量
        return ['hd', self::QUALITY_MAP['hd']];
    }

    /**
     * 质量降级处理
     *
     * @param array $streamInfos 流信息数组
     * @param int $currentIndex 当前质量索引
     * @return array 降级后的流URL信息
     * @throws Exception 所有质量都不可用时抛出异常
     */
    private function fallbackQuality(array $streamInfos, int $currentIndex): array
    {
        // 优先向下降级（更低质量）
        $nextIndex = $currentIndex + 1;
        if ($nextIndex >= count($streamInfos)) {
            // 无法向下降级，尝试向上降级
            $nextIndex = $currentIndex - 1;
        }

        if ($nextIndex >= 0 && $nextIndex < count($streamInfos)) {
            $fallbackUrl = $streamInfos[$nextIndex]['url'];
            $fallbackQuality = $streamInfos[$nextIndex]['name'];

            if ($this->checkUrlAvailability($fallbackUrl)) {
                $this->log("成功降级到 {$fallbackQuality} 质量");
                return [
                    'url' => $fallbackUrl,
                    'quality' => $fallbackQuality,
                    'index' => $nextIndex
                ];
            }
        }

        // 尝试所有其他质量
        foreach ($streamInfos as $index => $streamInfo) {
            if ($index === $currentIndex) continue;

            if ($this->checkUrlAvailability($streamInfo['url'])) {
                $this->log("降级到 {$streamInfo['name']} 质量");
                return [
                    'url' => $streamInfo['url'],
                    'quality' => $streamInfo['name'],
                    'index' => $index
                ];
            }
        }

        throw new Exception('所有质量的流URL都不可用');
    }

    /**
     * 发送HTTP请求
     *
     * @param string $url 请求URL
     * @param array $customHeaders 自定义请求头
     * @return string 响应内容
     * @throws Exception 请求失败时抛出异常
     */
    private function httpRequest(string $url, array $customHeaders = []): string
    {
        $headers = array_merge($this->defaultHeaders, $this->customHeaders, $customHeaders);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_ENCODING => 'gzip, deflate, br',
            CURLOPT_HTTPHEADER => $this->buildHttpHeaders($headers)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("HTTP请求失败: {$error}");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }

        $this->log("HTTP请求成功: {$url} (长度: " . strlen($response) . " 字节)");
        return $response;
    }

    /**
     * 构建HTTP请求头数组
     *
     * @param array $headers 请求头关联数组
     * @return array cURL格式的请求头数组
     */
    private function buildHttpHeaders(array $headers): array
    {
        $curlHeaders = [];
        foreach ($headers as $key => $value) {
            $curlHeaders[] = "{$key}: {$value}";
        }
        return $curlHeaders;
    }

    /**
     * 日志输出
     *
     * @param string $message 日志消息
     */
    private function log(string $message): void
    {
        if ($this->debug) {
            echo "[" . date('Y-m-d H:i:s') . "] {$message}\n";
        }
    }

    /**
     * 设置自定义Cookie
     *
     * @param string $cookie Cookie字符串
     */
    public function setCookie(string $cookie): void
    {
        $this->customHeaders['Cookie'] = $cookie;
    }

    /**
     * 设置自定义User-Agent
     *
     * @param string $userAgent User-Agent字符串
     */
    public function setUserAgent(string $userAgent): void
    {
        $this->customHeaders['User-Agent'] = $userAgent;
    }

    /**
     * 获取支持的清晰度列表
     *
     * @return array 清晰度列表
     */
    public static function getSupportedQualities(): array
    {
        return array_keys(self::QUALITY_MAP);
    }
}
