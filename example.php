<?php

require_once 'DouyinLiveParser.php';

/**
 * 抖音直播解析器使用示例
 * 
 * 本示例展示了如何使用 DouyinLiveParser 类解析抖音直播间
 * 并获取不同清晰度的流URL
 */

// 示例直播间URL（请替换为实际的直播间URL）
$liveUrls = [
    'https://live.douyin.com/123456789',  // Web版URL
    'https://v.douyin.com/iJsKqPeR/',     // App版URL（短链接）
];

// 创建解析器实例（开启调试模式）
$parser = new DouyinLiveParser([], 10, true);

// 可选：设置自定义Cookie（如果需要）
// $parser->setCookie('your_custom_cookie_here');

// 可选：设置自定义User-Agent
// $parser->setUserAgent('Custom User Agent');

echo "=== 抖音直播解析器测试 ===\n\n";

foreach ($liveUrls as $index => $url) {
    echo "测试URL " . ($index + 1) . ": {$url}\n";
    echo str_repeat('-', 50) . "\n";
    
    try {
        // 第一步：获取直播间信息
        echo "1. 获取直播间信息...\n";
        $roomInfo = $parser->getRoomInfo($url);
        
        // 显示基本信息
        echo "   主播名称: {$roomInfo['roomInfo']['hostName']}\n";
        echo "   直播标题: {$roomInfo['roomInfo']['roomName']}\n";
        echo "   直播状态: " . ($roomInfo['roomInfo']['status'] ? '直播中' : '未直播') . "\n";
        
        if (!$roomInfo['roomInfo']['status']) {
            echo "   ⚠️  当前未在直播，无法获取流URL\n\n";
            continue;
        }
        
        // 第二步：获取不同清晰度的流URL
        echo "\n2. 获取流URL...\n";
        
        // 测试不同清晰度
        $qualities = ['origin', 'uhd', 'hd', 'sd', 'ld'];
        
        foreach ($qualities as $quality) {
            try {
                echo "   尝试获取 {$quality} 质量...\n";
                $streamResult = $parser->getStreamUrls($roomInfo, $quality);
                
                echo "   ✅ 成功获取 {$streamResult['quality']} 质量流URL\n";
                echo "   📺 URL: {$streamResult['url']}\n";
                echo "   🎯 索引: {$streamResult['index']}\n";
                
                // 只测试第一个成功的质量
                break;
                
            } catch (Exception $e) {
                echo "   ❌ {$quality} 质量获取失败: {$e->getMessage()}\n";
            }
        }
        
        // 第三步：测试URL可用性检测
        echo "\n3. 测试URL可用性检测...\n";
        if (isset($streamResult)) {
            $isAvailable = $parser->checkUrlAvailability($streamResult['url']);
            echo "   URL可用性: " . ($isAvailable ? '✅ 可用' : '❌ 不可用') . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 解析失败: {$e->getMessage()}\n";
    }
    
    echo "\n" . str_repeat('=', 60) . "\n\n";
}

// 显示支持的清晰度列表
echo "支持的清晰度列表:\n";
$qualities = DouyinLiveParser::getSupportedQualities();
foreach ($qualities as $index => $quality) {
    echo "  {$index}. {$quality}\n";
}

echo "\n=== 测试完成 ===\n";

/**
 * 高级用法示例
 */
function advancedExample()
{
    echo "\n=== 高级用法示例 ===\n";
    
    // 创建解析器（自定义配置）
    $customHeaders = [
        'X-Custom-Header' => 'CustomValue'
    ];
    $parser = new DouyinLiveParser($customHeaders, 15, true);
    
    // 设置自定义Cookie（模拟登录状态）
    $customCookie = 'sessionid=abc123; userid=456789; login_status=1';
    $parser->setCookie($customCookie);
    
    $url = 'https://live.douyin.com/123456789';
    
    try {
        // 获取房间信息
        $roomInfo = $parser->getRoomInfo($url);
        
        if ($roomInfo['roomInfo']['status']) {
            // 智能质量选择：根据网络状况选择合适的质量
            $networkSpeed = 5000; // 假设网络速度 5Mbps
            $recommendedQuality = selectQualityByNetwork($networkSpeed);
            
            echo "推荐质量: {$recommendedQuality}\n";
            
            // 获取推荐质量的流URL
            $streamResult = $parser->getStreamUrls($roomInfo, $recommendedQuality);
            echo "获取到流URL: {$streamResult['url']}\n";
            
            // 批量检测所有质量的可用性
            $allQualities = DouyinLiveParser::getSupportedQualities();
            echo "\n所有质量可用性检测:\n";
            
            foreach ($allQualities as $quality) {
                try {
                    $result = $parser->getStreamUrls($roomInfo, $quality);
                    $available = $parser->checkUrlAvailability($result['url']);
                    echo "  {$quality}: " . ($available ? '✅' : '❌') . "\n";
                } catch (Exception $e) {
                    echo "  {$quality}: ❌ (获取失败)\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "高级示例执行失败: {$e->getMessage()}\n";
    }
}

/**
 * 根据网络速度选择合适的质量
 * 
 * @param int $speedKbps 网络速度（Kbps）
 * @return string 推荐的质量等级
 */
function selectQualityByNetwork(int $speedKbps): string
{
    if ($speedKbps >= 10000) {
        return 'origin';  // 10Mbps+ 选择原画
    } elseif ($speedKbps >= 5000) {
        return 'uhd';     // 5Mbps+ 选择超高清
    } elseif ($speedKbps >= 2000) {
        return 'hd';      // 2Mbps+ 选择高清
    } elseif ($speedKbps >= 1000) {
        return 'sd';      // 1Mbps+ 选择标清
    } else {
        return 'ld';      // 低速网络选择流畅
    }
}

/**
 * 错误处理示例
 */
function errorHandlingExample()
{
    echo "\n=== 错误处理示例 ===\n";
    
    $parser = new DouyinLiveParser([], 5, true);
    
    // 测试无效URL
    $invalidUrls = [
        'https://invalid-domain.com/123',
        'https://live.douyin.com/invalid-room',
        'not-a-url-at-all'
    ];
    
    foreach ($invalidUrls as $url) {
        echo "测试无效URL: {$url}\n";
        try {
            $roomInfo = $parser->getRoomInfo($url);
            echo "  意外成功（这不应该发生）\n";
        } catch (Exception $e) {
            echo "  ✅ 正确捕获错误: {$e->getMessage()}\n";
        }
    }
}

/**
 * 性能测试示例
 */
function performanceTest()
{
    echo "\n=== 性能测试示例 ===\n";
    
    $parser = new DouyinLiveParser([], 10, false); // 关闭调试模式
    $url = 'https://live.douyin.com/123456789';
    
    $startTime = microtime(true);
    
    try {
        // 测试解析性能
        $roomInfo = $parser->getRoomInfo($url);
        $parseTime = microtime(true) - $startTime;
        
        echo "解析耗时: " . round($parseTime * 1000, 2) . " ms\n";
        
        if ($roomInfo['roomInfo']['status']) {
            // 测试URL检测性能
            $checkStartTime = microtime(true);
            $streamResult = $parser->getStreamUrls($roomInfo, 'hd');
            $available = $parser->checkUrlAvailability($streamResult['url']);
            $checkTime = microtime(true) - $checkStartTime;
            
            echo "URL检测耗时: " . round($checkTime * 1000, 2) . " ms\n";
            echo "总耗时: " . round(($parseTime + $checkTime) * 1000, 2) . " ms\n";
        }
        
    } catch (Exception $e) {
        echo "性能测试失败: {$e->getMessage()}\n";
    }
}

// 运行高级示例（取消注释以运行）
// advancedExample();
// errorHandlingExample();
// performanceTest();
