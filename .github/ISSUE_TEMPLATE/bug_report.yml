name: 报告程序错误
description: 报告程序使用过程中遇到的问题。
labels: ["bug"]
body:
  - type: input
    id: subject
    validations:
      required: true
    attributes:
      label: 程序版本
      description: 例如 0.6.4、0.7.1 等等。

  - type: textarea
    id: config
    validations:
      required: true
    attributes:
      label: 所使用的 config 文件的内容
      placeholder: 如果启动时没有使用 config，则此项留空
      description: |
        启动项的 -c 后指定的 yaml 格式的文件的内容。

  - type: textarea
    id: log
    validations:
      required: true
    attributes:
      label: 程序 log
      description: 错误发生前后程序输出的错误信息。
  
  - type: textarea
    id: content
    validations:
      required: true
    attributes:
      label: 自由描述
      description: 详细介绍您所遇到的问题。