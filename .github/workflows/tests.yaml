name: CI
on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - '*'
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.24
      - name: Setup Node.js environment
        uses: actions/setup-node@v2.4.0
      - name: Build
        run: make build-web bililive
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.24
      - name: Setup Node.js environment
        uses: actions/setup-node@v2.4.0
      - name: Test
        run: make build-web test
      - name: Codecov
        uses: codecov/codecov-action@v2
  lint:
    name: Lint
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    timeout-minutes: 3
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.24
      - uses: golangci/golangci-lint-action@v8
        with:
          working-directory: src/
          args: --build-tags=dev