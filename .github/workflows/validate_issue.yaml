name: Validate issue
on:
  issues:
    types:
      - labeled

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - name: Check if issue is labeled within one minute
        id: check-time
        uses: actions/github-script@v6
        with:
          script: |
            const createdAt = new Date("${{ github.event.issue.created_at }}");
            const now = new Date();
            const oneMinute = 60 * 1000; // milliseconds
            if (now - createdAt > oneMinute) {
              console.log('Issue was not labeled within one minute. Skipping...');
              return false;
            }
            return true;
          result-encoding: string

      - name: Check if issue is labeled as bug or question
        id: check-label
        if: steps.check-time.outputs.result == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const labels = JSON.parse(`${{ toJSON(github.event.issue.labels) }}`).map(label => label.name);
            const isBugOrQuestion = labels.includes('bug') || labels.includes('question');
            core.setOutput('isBugOrQuestion', isBugOrQuestion);

      - name: Validate issues
        if: steps.check-label.outputs.isBugOrQuestion == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const body = "${{ github.event.issue.body }}";
            const versionRegex = /### 程序版本\s*(.+)\s*### 所使用的/;
            const versionMatch = body.match(versionRegex);
            let closeReasons = [];
            if (!versionMatch || !versionMatch[1].match(/\d+/)) {
              closeReasons.push('请填入有效的程序版本。');
            }
            if (closeReasons.length > 0) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: `Issue 未通过验证：\n${closeReasons.join('\n')}`,
              });
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                state: 'closed'
              });
            }
          github-token: ${{ secrets.GITHUB_TOKEN }}