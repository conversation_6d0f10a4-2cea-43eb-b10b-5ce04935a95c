name: Release
on:
  push:
    tags:
      - 'v*'
jobs:
  release-bins:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - uses: actions/setup-go@v2
        with:
          go-version: 1.24
      - run: go install github.com/golang/mock/mockgen@v1.6.0
      - uses: actions/setup-node@v2.4.0
      - name: Build Web Page
        run: make build-web
      - name: Build go binaries
        run: make release
      - uses: softprops/action-gh-release@v1
        with:
          files: 'bin/*'
          prerelease: ${{ contains(env.GIT_TAG, 'rc') }}
  release-docker-images:
    runs-on: ubuntu-latest
    needs:
      - release-bins
    steps:
      - uses: actions/checkout@v2
      - run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - run: if ! echo $GIT_TAG | grep "rc" >/dev/null; then DOCKER_TAGS=chigusa/bililive-go:$GIT_TAG,chigusa/bililive-go:latest; else DOCKER_TAGS=chigusa/bililive-go:$GIT_TAG; fi; echo "DOCKER_TAGS=$DOCKER_TAGS" >> $GITHUB_ENV
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Docker Setup Buildx
        uses: docker/setup-buildx-action@v1.5.1
      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      - name: Build the Docker image
        uses: docker/build-push-action@v2.6.1
        with:
          file: Dockerfile
          build-args: tag=${{ env.GIT_TAG }}
          platforms: linux/amd64,linux/386,linux/arm64/v8,linux/arm/v7,linux/arm/v6
          push: true
          tags: ${{ env.DOCKER_TAGS }}
